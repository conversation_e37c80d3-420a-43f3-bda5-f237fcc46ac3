/**
 * إصلاح مبسط لبنية جدول الخزينة
 *
 * هذا الملف يقوم بإصلاح مشكلة تحديث الرصيد الافتتاحي عن طريق:
 * 1. التحقق من بنية جدول الخزينة
 * 2. إضافة الأعمدة المفقودة
 * 3. تحديث القيم الافتراضية
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// تحديد مسار قاعدة البيانات (نفس المسار المستخدم في التطبيق)
const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'wms-dev');
const dbDir = path.join(userDataPath, 'wms-database');
let dbPath = path.join(dbDir, 'warehouse.db');

console.log('🔧 بدء إصلاح بنية جدول الخزينة...');
console.log(`📁 مسار قاعدة البيانات: ${dbPath}`);

// التحقق من وجود ملف قاعدة البيانات
if (!fs.existsSync(dbPath)) {
  console.log('❌ ملف قاعدة البيانات غير موجود');
  console.log(`💡 المسار المتوقع: ${dbPath}`);
  console.log('💡 يرجى تشغيل التطبيق أولاً لإنشاء قاعدة البيانات');

  // البحث عن ملف قاعدة البيانات في مواقع أخرى محتملة
  const possiblePaths = [
    path.join(__dirname, 'database.db'),
    path.join(__dirname, 'warehouse.db'),
    path.join(userDataPath, 'database.db'),
    path.join(userDataPath, 'warehouse.db')
  ];

  console.log('🔍 البحث عن ملف قاعدة البيانات في مواقع أخرى...');
  let foundPath = null;

  for (const possiblePath of possiblePaths) {
    if (fs.existsSync(possiblePath)) {
      console.log(`✅ تم العثور على قاعدة البيانات في: ${possiblePath}`);
      foundPath = possiblePath;
      break;
    }
  }

  if (!foundPath) {
    console.log('❌ لم يتم العثور على ملف قاعدة البيانات في أي مكان');
    process.exit(1);
  }

  // استخدام المسار الذي تم العثور عليه
  dbPath = foundPath;
}

try {
  // استخدام sqlite3 بدلاً من better-sqlite3
  const sqlite3 = require('sqlite3').verbose();

  // فتح قاعدة البيانات
  const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
      console.error('❌ خطأ في فتح قاعدة البيانات:', err.message);
      process.exit(1);
    }
    console.log('✅ تم فتح قاعدة البيانات بنجاح');
  });

  // التحقق من وجود جدول الخزينة
  db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='cashbox'", (err, row) => {
    if (err) {
      console.error('❌ خطأ في التحقق من وجود جدول الخزينة:', err.message);
      db.close();
      process.exit(1);
    }

    if (!row) {
      console.log('❌ جدول الخزينة غير موجود');
      db.close();
      process.exit(1);
    }

    console.log('✅ جدول الخزينة موجود');

    // الحصول على معلومات الجدول الحالي
    db.all("PRAGMA table_info(cashbox)", (err, tableInfo) => {
      if (err) {
        console.error('❌ خطأ في الحصول على معلومات الجدول:', err.message);
        db.close();
        process.exit(1);
      }

      const existingColumns = tableInfo.map(col => col.name);
      console.log('📋 الأعمدة الموجودة حالياً:', existingColumns);

      // قائمة الأعمدة المطلوبة
      const requiredColumns = [
        { name: 'sales_total', type: 'REAL', default: '0' },
        { name: 'purchases_total', type: 'REAL', default: '0' },
        { name: 'returns_total', type: 'REAL', default: '0' },
        { name: 'transport_total', type: 'REAL', default: '0' }
      ];

      console.log('🔍 التحقق من الأعمدة المطلوبة...');

      let columnsToAdd = [];

      // التحقق من الأعمدة المفقودة
      for (const column of requiredColumns) {
        if (!existingColumns.includes(column.name)) {
          columnsToAdd.push(column);
          console.log(`➕ العمود ${column.name} مفقود وسيتم إضافته`);
        } else {
          console.log(`✅ العمود ${column.name} موجود`);
        }
      }

      if (columnsToAdd.length === 0) {
        console.log('✅ جميع الأعمدة المطلوبة موجودة');

        // التحقق من البيانات
        checkCashboxData(db);
      } else {
        console.log(`📊 سيتم إضافة ${columnsToAdd.length} عمود جديد`);

        // إضافة الأعمدة المفقودة
        addMissingColumns(db, columnsToAdd, () => {
          checkCashboxData(db);
        });
      }
    });
  });

  // دالة إضافة الأعمدة المفقودة
  function addMissingColumns(db, columns, callback) {
    let addedCount = 0;

    function addNextColumn() {
      if (addedCount >= columns.length) {
        console.log(`✅ تم إضافة ${addedCount} عمود بنجاح`);
        callback();
        return;
      }

      const column = columns[addedCount];
      const alterQuery = `ALTER TABLE cashbox ADD COLUMN ${column.name} ${column.type} DEFAULT ${column.default}`;

      console.log(`➕ إضافة العمود: ${column.name}`);

      db.run(alterQuery, (err) => {
        if (err) {
          console.error(`❌ خطأ في إضافة العمود ${column.name}:`, err.message);
        } else {
          console.log(`✅ تم إضافة العمود: ${column.name}`);
        }

        addedCount++;
        addNextColumn();
      });
    }

    addNextColumn();
  }

  // دالة التحقق من بيانات الخزينة
  function checkCashboxData(db) {
    db.get('SELECT * FROM cashbox LIMIT 1', (err, cashboxData) => {
      if (err) {
        console.error('❌ خطأ في الحصول على بيانات الخزينة:', err.message);
        db.close();
        return;
      }

      if (!cashboxData) {
        console.log('⚠️  لا توجد بيانات في جدول الخزينة');
        console.log('💡 يمكنك إنشاء خزينة جديدة من واجهة التطبيق');
        db.close();
        return;
      }

      console.log('✅ تم العثور على بيانات الخزينة:');
      console.log('   - الرصيد الافتتاحي:', cashboxData.initial_balance || 0);
      console.log('   - الرصيد الحالي:', cashboxData.current_balance || 0);
      console.log('   - إجمالي الأرباح:', cashboxData.profit_total || 0);
      console.log('   - إجمالي المبيعات:', cashboxData.sales_total || 0);
      console.log('   - إجمالي المشتريات:', cashboxData.purchases_total || 0);
      console.log('   - إجمالي المرتجعات:', cashboxData.returns_total || 0);
      console.log('   - إجمالي مصاريف النقل:', cashboxData.transport_total || 0);

      // تحديث القيم الافتراضية للأعمدة الجديدة إذا كانت NULL
      console.log('🔄 تحديث القيم الافتراضية...');

      const updateQuery = `
        UPDATE cashbox
        SET
          profit_total = COALESCE(profit_total, 0),
          sales_total = COALESCE(sales_total, 0),
          purchases_total = COALESCE(purchases_total, 0),
          returns_total = COALESCE(returns_total, 0),
          transport_total = COALESCE(transport_total, 0),
          updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;

      db.run(updateQuery, [cashboxData.id], function(err) {
        if (err) {
          console.error('❌ خطأ في تحديث القيم الافتراضية:', err.message);
        } else {
          console.log(`✅ تم تحديث ${this.changes} سجل`);
        }

        // اختبار تحديث الرصيد الافتتاحي
        testInitialBalanceUpdate(db, cashboxData);
      });
    });
  }

  // دالة اختبار تحديث الرصيد الافتتاحي
  function testInitialBalanceUpdate(db, cashboxData) {
    console.log('🧪 اختبار تحديث الرصيد الافتتاحي...');

    const testInitialBalance = 1000;
    const updateInitialBalanceQuery = `
      UPDATE cashbox
      SET
        initial_balance = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    db.run(updateInitialBalanceQuery, [testInitialBalance, cashboxData.id], function(err) {
      if (err) {
        console.error('❌ خطأ في اختبار تحديث الرصيد الافتتاحي:', err.message);
      } else if (this.changes > 0) {
        console.log('✅ اختبار تحديث الرصيد الافتتاحي نجح');

        // إعادة الرصيد الافتتاحي إلى قيمته الأصلية
        db.run(updateInitialBalanceQuery, [cashboxData.initial_balance, cashboxData.id], function(err) {
          if (err) {
            console.error('❌ خطأ في إعادة الرصيد الافتتاحي:', err.message);
          } else {
            console.log('✅ تم إعادة الرصيد الافتتاحي إلى قيمته الأصلية');
          }

          // عرض بنية الجدول النهائية
          showFinalTableStructure(db);
        });
      } else {
        console.log('❌ فشل اختبار تحديث الرصيد الافتتاحي');
        showFinalTableStructure(db);
      }
    });
  }

  // دالة عرض بنية الجدول النهائية
  function showFinalTableStructure(db) {
    console.log('📋 بنية الجدول النهائية:');

    db.all("PRAGMA table_info(cashbox)", (err, finalTableInfo) => {
      if (err) {
        console.error('❌ خطأ في الحصول على بنية الجدول النهائية:', err.message);
      } else {
        finalTableInfo.forEach(col => {
          console.log(`   - ${col.name}: ${col.type} ${col.notnull ? 'NOT NULL' : ''} ${col.dflt_value ? `DEFAULT ${col.dflt_value}` : ''}`);
        });
      }

      // إغلاق قاعدة البيانات
      db.close((err) => {
        if (err) {
          console.error('❌ خطأ في إغلاق قاعدة البيانات:', err.message);
        } else {
          console.log('✅ تم إكمال إصلاح بنية جدول الخزينة بنجاح');
          console.log('💡 يمكنك الآن تحديث الرصيد الافتتاحي من واجهة التطبيق');
        }
      });
    });
  }

} catch (error) {
  console.error('❌ خطأ في إصلاح بنية جدول الخزينة:', error);
  process.exit(1);
}
